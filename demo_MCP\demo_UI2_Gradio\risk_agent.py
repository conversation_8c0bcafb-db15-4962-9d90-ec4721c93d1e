"""
航天质量风险管理智能体 RiskAgent
基于LangGraph框架构建，集成qwen3大模型和text2vector工具
"""

import os
import json
import asyncio
from typing import List, Dict, Any, Optional, Annotated
from datetime import datetime

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode
from typing_extensions import TypedDict

from mcp_text2vector import MCPText2Vector, create_text2vector_tool


class AgentState(TypedDict):
    """智能体状态定义"""
    messages: Annotated[list, add_messages]
    vector_tool: Optional[MCPText2Vector]
    current_task: str
    context: Dict[str, Any]


class RiskAgent:
    """航天质量风险管理智能体"""
    
    def __init__(self, 
                 api_key: str = "sk-837d6f3064d249d5b83c120af69222cb",
                 base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1",
                 model_name: str = "qwen3-max"):
        """
        初始化RiskAgent
        
        Args:
            api_key: API密钥
            base_url: API基础URL
            model_name: 模型名称
        """
        self.api_key = api_key
        self.base_url = base_url
        self.model_name = model_name
        
        # 初始化大模型 - 使用dashscope
        try:
            from dashscope import Generation
            self.use_dashscope = True
            self.dashscope_api_key = api_key
            self.dashscope_model = model_name
        except ImportError:
            # 如果没有dashscope，使用OpenAI兼容接口
            self.use_dashscope = False
            self.llm = ChatOpenAI(
                api_key=api_key,
                base_url=base_url,
                model=model_name,
                temperature=0.1,
                max_tokens=2000
            )
        
        # 初始化向量工具
        self.vector_tool = create_text2vector_tool()
        
        # 创建工具列表
        self.tools = [
            self.search_risk_knowledge,
            self.process_markdown_file,
            self.get_vector_stats,
            self.generate_risk_report
        ]
        
        # 创建工具节点
        self.tool_node = ToolNode(self.tools)
        
        # 构建图
        self.graph = self._build_graph()
        
        # 系统提示词
        self.system_prompt = """你是航天质量风险管理智能体RiskAgent，专门协助用户进行航天质量风险管理工作。

你的主要能力包括：
1. 风险知识检索：从专业的航天风险管理知识库中检索相关信息
2. 文件处理：将MD格式的风险管理文档向量化并存储到知识库
3. 风险分析：基于知识库内容进行风险识别、分析和评估
4. 报告生成：生成专业的风险管理报告

请始终保持专业、准确、有条理的回答风格，并充分利用知识库中的信息来支持你的分析和建议。

当用户询问风险相关问题时，请：
1. 首先使用search_risk_knowledge工具检索相关知识
2. 基于检索结果进行分析和回答
3. 提供具体的风险应对措施和建议
4. 如果需要，可以生成详细的风险管理报告

可用工具：
- search_risk_knowledge: 检索风险管理知识
- process_markdown_file: 处理MD文件并向量化
- get_vector_stats: 获取知识库统计信息
- generate_risk_report: 生成风险管理报告"""
    
    @tool
    def search_risk_knowledge(self, query: str, n_results: int = 5) -> List[Dict[str, Any]]:
        """
        检索风险管理知识
        
        Args:
            query: 查询内容
            n_results: 返回结果数量
            
        Returns:
            检索结果列表
        """
        try:
            results = self.vector_tool.search_similar(query, n_results)
            return results
        except Exception as e:
            return [{"error": f"检索失败: {str(e)}"}]
    
    @tool
    def process_markdown_file(self, file_path: str) -> Dict[str, Any]:
        """
        处理Markdown文件并向量化存储
        
        Args:
            file_path: 文件路径
            
        Returns:
            处理结果
        """
        try:
            result = self.vector_tool.process_markdown_file(file_path)
            return result
        except Exception as e:
            return {"success": False, "message": f"处理文件失败: {str(e)}"}
    
    @tool
    def get_vector_stats(self) -> Dict[str, Any]:
        """
        获取向量数据库统计信息
        
        Returns:
            统计信息
        """
        try:
            stats = self.vector_tool.get_collection_stats()
            return stats
        except Exception as e:
            return {"error": f"获取统计信息失败: {str(e)}"}
    
    @tool
    def generate_risk_report(self, 
                           risk_type: str, 
                           analysis_content: str,
                           recommendations: str = "") -> str:
        """
        生成风险管理报告
        
        Args:
            risk_type: 风险类型
            analysis_content: 分析内容
            recommendations: 建议措施
            
        Returns:
            格式化的报告内容
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        report = f"""# 航天质量风险管理报告

## 基本信息
- **报告生成时间**: {timestamp}
- **风险类型**: {risk_type}
- **生成系统**: 航天质量风险管理智能体RiskAgent

## 风险分析

{analysis_content}

## 应对建议

{recommendations if recommendations else "基于分析结果，建议采取相应的风险控制措施。"}

## 附录
本报告由RiskAgent基于航天质量风险管理知识库生成，仅供参考。具体实施时请结合实际情况进行调整。

---
*报告结束*
"""
        return report
    
    def _build_graph(self) -> StateGraph:
        """构建LangGraph图"""
        
        def should_continue(state: AgentState) -> str:
            """判断是否继续执行工具"""
            messages = state["messages"]
            last_message = messages[-1]
            
            # 如果最后一条消息包含工具调用，则执行工具
            if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                return "tools"
            return END
        
        def call_model(state: AgentState) -> Dict[str, Any]:
            """调用模型"""
            messages = state["messages"]

            # 添加系统提示词
            if not messages or not isinstance(messages[0], SystemMessage):
                messages = [SystemMessage(content=self.system_prompt)] + messages

            # 调用模型
            # if self.use_dashscope:
            #     response = self._call_dashscope(messages)
            # else:
                response = self.llm.bind_tools(self.tools).invoke(messages)

            return {"messages": [response]}
        
        def call_tools(state: AgentState) -> Dict[str, Any]:
            """调用工具"""
            return self.tool_node.invoke(state)
        
        # 创建图
        workflow = StateGraph(AgentState)
        
        # 添加节点
        workflow.add_node("agent", call_model)
        workflow.add_node("tools", call_tools)
        
        # 设置入口点
        workflow.set_entry_point("agent")
        
        # 添加条件边
        workflow.add_conditional_edges(
            "agent",
            should_continue,
            {
                "tools": "tools",
                END: END
            }
        )
        
        # 工具执行后返回agent
        workflow.add_edge("tools", "agent")
        
        return workflow.compile()

    # def _call_dashscope(self, messages: List) -> AIMessage:
    #     """调用dashscope API"""
    #     try:
    #         from dashscope import Generation
    #         import dashscope

    #         # 设置API密钥
    #         dashscope.api_key = self.dashscope_api_key

    #         # 转换消息格式
    #         formatted_messages = []
    #         for msg in messages:
    #             if isinstance(msg, SystemMessage):
    #                 formatted_messages.append({"role": "system", "content": msg.content})
    #             elif isinstance(msg, HumanMessage):
    #                 formatted_messages.append({"role": "user", "content": msg.content})
    #             elif isinstance(msg, AIMessage):
    #                 formatted_messages.append({"role": "assistant", "content": msg.content})

    #         # 调用API
    #         response = Generation.call(
    #             model=self.dashscope_model,
    #             messages=formatted_messages,
    #             temperature=0.1,
    #             max_tokens=2000,
    #             result_format='message'
    #         )

    #         if response.status_code == 200:
    #             content = response.output.choices[0].message.content
    #             return AIMessage(content=content)
    #         else:
    #             error_msg = f"Dashscope API调用失败: {response.message}"
    #             return AIMessage(content=error_msg)

    #     except Exception as e:
    #         error_msg = f"调用Dashscope时出错: {str(e)}"
    #         return AIMessage(content=error_msg)
    
    def chat(self, message: str, context: Dict[str, Any] = None) -> str:
        """
        与智能体对话
        
        Args:
            message: 用户消息
            context: 上下文信息
            
        Returns:
            智能体回复
        """
        try:
            # 构建初始状态
            initial_state = {
                "messages": [HumanMessage(content=message)],
                "vector_tool": self.vector_tool,
                "current_task": "chat",
                "context": context or {}
            }
            
            # 执行图
            result = self.graph.invoke(initial_state)
            
            # 获取最后一条AI消息
            ai_messages = [msg for msg in result["messages"] if isinstance(msg, AIMessage)]
            if ai_messages:
                return ai_messages[-1].content
            else:
                return "抱歉，我无法处理您的请求。"
                
        except Exception as e:
            return f"处理请求时出错: {str(e)}"
    
    async def achat(self, message: str, context: Dict[str, Any] = None) -> str:
        """
        异步对话接口
        
        Args:
            message: 用户消息
            context: 上下文信息
            
        Returns:
            智能体回复
        """
        # 在实际应用中，这里可以使用异步版本的图执行
        return self.chat(message, context)
    
    def process_file_and_chat(self, file_path: str, query: str = None) -> Dict[str, Any]:
        """
        处理文件并进行对话
        
        Args:
            file_path: 文件路径
            query: 可选的查询内容
            
        Returns:
            处理结果和对话结果
        """
        # 处理文件
        process_result = self.vector_tool.process_markdown_file(file_path)
        
        result = {
            "file_process": process_result,
            "chat_response": None
        }
        
        # 如果有查询，则进行对话
        if query:
            if process_result["success"]:
                chat_message = f"我已经处理了文件 {file_path}，现在请回答：{query}"
            else:
                chat_message = f"处理文件 {file_path} 失败，但请尝试回答：{query}"
            
            result["chat_response"] = self.chat(chat_message)
        
        return result


# 创建全局智能体实例
_risk_agent = None

def get_risk_agent() -> RiskAgent:
    """获取RiskAgent实例（单例模式）"""
    global _risk_agent
    if _risk_agent is None:
        _risk_agent = RiskAgent()
    return _risk_agent


if __name__ == "__main__":
    # 测试代码
    agent = RiskAgent()
    
    print("=== 航天质量风险管理智能体RiskAgent测试 ===")
    
    # 测试基本对话
    print("\n1. 测试基本对话:")
    response = agent.chat("你好，请介绍一下你的功能")
    print(f"回复: {response}")
    
    # 测试知识检索
    print("\n2. 测试知识检索:")
    response = agent.chat("请检索关于风险识别的相关信息")
    print(f"回复: {response}")
    
    # 测试统计信息
    print("\n3. 测试统计信息:")
    response = agent.chat("请获取当前知识库的统计信息")
    print(f"回复: {response}")
    
    print("\n=== 测试完成 ===")
