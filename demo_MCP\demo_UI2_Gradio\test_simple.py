"""
简化测试版本
"""

import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer

# 创建简单测试
def test_simple():
    print("开始简单测试...")
    
    # 初始化模型
    print("加载模型...")
    encoder = SentenceTransformer("all-MiniLM-L6-v2")
    
    # 初始化数据库
    print("初始化数据库...")
    client = chromadb.PersistentClient(
        path="./test_db",
        settings=Settings(anonymized_telemetry=False)
    )
    
    # 创建集合
    try:
        collection = client.get_collection("test")
        client.delete_collection("test")
    except:
        pass
    
    collection = client.create_collection("test")
    
    # 测试数据
    texts = ["这是一个测试文本", "这是另一个测试文本"]
    embeddings = encoder.encode(texts).tolist()
    
    # 测试元数据
    metadatas = [
        {
            "file_path": "test.md",
            "chunk_index": 0,
            "content": texts[0],
            "type": "p"
        },
        {
            "file_path": "test.md", 
            "chunk_index": 1,
            "content": texts[1],
            "type": "p"
        }
    ]
    
    ids = ["test_0", "test_1"]
    
    print("存储数据...")
    try:
        collection.add(
            embeddings=embeddings,
            documents=texts,
            metadatas=metadatas,
            ids=ids
        )
        print("存储成功!")
        
        # 测试搜索
        print("测试搜索...")
        query_embedding = encoder.encode(["测试"]).tolist()[0]
        results = collection.query(
            query_embeddings=[query_embedding],
            n_results=2
        )
        print(f"搜索结果: {len(results['ids'][0])} 条")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple()
