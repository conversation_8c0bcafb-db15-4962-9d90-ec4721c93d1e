"""
简化版RiskAgent，用于测试基本功能
"""

import os
import json
from typing import List, Dict, Any, Optional
from datetime import datetime

from mcp_text2vector import MCPText2Vector, create_text2vector_tool


class SimpleRiskAgent:
    """简化版航天质量风险管理智能体"""
    
    def __init__(self):
        """初始化SimpleRiskAgent"""
        # 初始化向量工具
        self.vector_tool = create_text2vector_tool()
        
        # 系统提示词
        self.system_prompt = """你是航天质量风险管理智能体RiskAgent，专门协助用户进行航天质量风险管理工作。

你的主要能力包括：
1. 风险知识检索：从专业的航天风险管理知识库中检索相关信息
2. 文件处理：将MD格式的风险管理文档向量化并存储到知识库
3. 风险分析：基于知识库内容进行风险识别、分析和评估
4. 报告生成：生成专业的风险管理报告"""
    
    def search_risk_knowledge(self, query: str, n_results: int = 5) -> List[Dict[str, Any]]:
        """检索风险管理知识"""
        try:
            results = self.vector_tool.search_similar(query, n_results)
            return results
        except Exception as e:
            return [{"error": f"检索失败: {str(e)}"}]
    
    def process_markdown_file(self, file_path: str) -> Dict[str, Any]:
        """处理Markdown文件并向量化存储"""
        try:
            result = self.vector_tool.process_markdown_file(file_path)
            return result
        except Exception as e:
            return {"success": False, "message": f"处理文件失败: {str(e)}"}
    
    def get_vector_stats(self) -> Dict[str, Any]:
        """获取向量数据库统计信息"""
        try:
            stats = self.vector_tool.get_collection_stats()
            return stats
        except Exception as e:
            return {"error": f"获取统计信息失败: {str(e)}"}
    
    def generate_risk_report(self, 
                           risk_type: str, 
                           analysis_content: str,
                           recommendations: str = "") -> str:
        """生成风险管理报告"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        report = f"""# 航天质量风险管理报告

## 基本信息
- **报告生成时间**: {timestamp}
- **风险类型**: {risk_type}
- **生成系统**: 航天质量风险管理智能体RiskAgent

## 风险分析

{analysis_content}

## 应对建议

{recommendations if recommendations else "基于分析结果，建议采取相应的风险控制措施。"}

## 附录
本报告由RiskAgent基于航天质量风险管理知识库生成，仅供参考。具体实施时请结合实际情况进行调整。

---
*报告结束*
"""
        return report
    
    def chat(self, message: str) -> str:
        """简化的对话接口"""
        message_lower = message.lower()
        
        # 根据关键词判断用户意图
        if "检索" in message or "搜索" in message or "查找" in message:
            # 提取查询关键词
            query = message.replace("检索", "").replace("搜索", "").replace("查找", "").strip()
            if not query:
                return "请提供要检索的关键词。"
            
            results = self.search_risk_knowledge(query, 3)
            if results and not results[0].get("error"):
                response = f"检索到 {len(results)} 条相关信息：\n\n"
                for i, result in enumerate(results, 1):
                    response += f"{i}. {result['content'][:200]}...\n"
                    response += f"   相似度: {result['similarity']:.3f}\n"
                    response += f"   来源: {result['file_path']}\n\n"
                return response
            else:
                return f"检索失败或未找到相关信息: {results[0].get('error', '未知错误')}"
        
        elif "统计" in message or "信息" in message:
            stats = self.get_vector_stats()
            if "error" not in stats:
                return f"""知识库统计信息：
- 总文档片段数: {stats['total_chunks']}
- 总文件数: {stats['total_files']}
- 集合名称: {stats['collection_name']}
- 包含文件: {', '.join(stats['files'])}"""
            else:
                return f"获取统计信息失败: {stats['error']}"
        
        elif "处理文件" in message or "向量化" in message:
            return "请使用process_file方法处理文件，或在界面中选择文件进行处理。"
        
        elif "报告" in message:
            return "请使用generate_report方法生成报告，需要提供风险类型和分析内容。"
        
        elif "功能" in message or "介绍" in message or "你好" in message:
            return f"""您好！我是航天质量风险管理智能体RiskAgent。

{self.system_prompt}

您可以：
1. 说"检索 [关键词]"来搜索风险管理知识
2. 说"统计信息"来查看知识库状态
3. 使用相应方法处理文件和生成报告

当前知识库状态：{self.get_vector_stats()['total_chunks']}个文档片段"""
        
        else:
            # 尝试进行知识检索
            results = self.search_risk_knowledge(message, 2)
            if results and not results[0].get("error"):
                response = f"基于您的问题，我找到了以下相关信息：\n\n"
                for i, result in enumerate(results, 1):
                    response += f"{i}. {result['content'][:300]}...\n"
                    response += f"   相似度: {result['similarity']:.3f}\n\n"
                
                response += "如需更详细的分析，请提供更具体的问题或使用'检索'命令。"
                return response
            else:
                return "抱歉，我无法理解您的问题。请尝试使用'检索 [关键词]'来搜索相关信息，或说'功能介绍'了解我的能力。"
    
    def process_file_and_chat(self, file_path: str, query: str = None) -> Dict[str, Any]:
        """处理文件并进行对话"""
        # 处理文件
        process_result = self.process_markdown_file(file_path)
        
        result = {
            "file_process": process_result,
            "chat_response": None
        }
        
        # 如果有查询，则进行对话
        if query:
            if process_result["success"]:
                chat_message = f"文件处理成功，现在回答您的问题：{query}"
                result["chat_response"] = self.chat(query)
            else:
                result["chat_response"] = f"文件处理失败：{process_result['message']}"
        
        return result


# 创建全局智能体实例
_simple_risk_agent = None

def get_simple_risk_agent() -> SimpleRiskAgent:
    """获取SimpleRiskAgent实例（单例模式）"""
    global _simple_risk_agent
    if _simple_risk_agent is None:
        _simple_risk_agent = SimpleRiskAgent()
    return _simple_risk_agent


if __name__ == "__main__":
    # 测试代码
    agent = SimpleRiskAgent()
    
    print("=== 简化版航天质量风险管理智能体测试 ===")
    
    # 测试基本对话
    print("\n1. 测试基本对话:")
    response = agent.chat("你好，请介绍一下你的功能")
    print(f"回复: {response}")
    
    # 测试知识检索
    print("\n2. 测试知识检索:")
    response = agent.chat("检索 风险识别")
    print(f"回复: {response}")
    
    # 测试统计信息
    print("\n3. 测试统计信息:")
    response = agent.chat("统计信息")
    print(f"回复: {response}")
    
    # 测试问答
    print("\n4. 测试问答:")
    response = agent.chat("什么是技术风险管理？")
    print(f"回复: {response}")
    
    print("\n=== 测试完成 ===")
