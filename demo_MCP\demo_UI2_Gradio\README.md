# 航天质量风险管理智能体 RiskAgent

## 🎯 项目简介

RiskAgent是一个基于LangGraph框架和MCP协议的航天质量风险管理智能体系统，专门用于航天质量风险管理工作。系统集成了文档向量化、智能问答、知识检索和报告生成等核心功能。

## ✨ 主要功能

- 🤖 **智能对话**: 支持自然语言问答，基于专业知识库回答航天风险管理问题
- 🔍 **知识检索**: 语义搜索功能，从1683个文档片段中快速找到相关信息
- 📁 **文件处理**: 自动处理Markdown文件，进行向量化存储
- 📊 **报告生成**: 生成专业的航天质量风险管理报告

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- Conda环境管理器
- 2GB+ 可用内存

### 2. 环境配置

```bash
# 激活conda环境
conda activate langgraph-env

# 安装依赖包（如果需要）
pip install -r requirements.txt
```

### 3. 启动系统

#### 方法一：使用启动脚本（推荐）
```bash
# Windows
start.bat

# Linux/Mac
./start.sh
```

#### 方法二：手动启动
```bash
python gradio_interface.py
```

### 4. 访问界面

打开浏览器访问：http://localhost:7860

## 📋 界面功能

### 智能对话标签页
- 与RiskAgent进行自然语言对话
- 询问航天风险管理相关问题
- 获得基于知识库的专业回答

### 知识检索标签页
- 输入关键词进行语义搜索
- 查看搜索结果和相似度评分
- 浏览相关文档片段

### 文件处理标签页
- 上传Markdown格式的风险管理文档
- 自动进行文本分块和向量化
- 存储到知识库中

### 报告生成标签页
- 选择风险类型
- 输入分析内容和建议
- 生成标准化的风险管理报告

## 🔧 系统架构

```
RiskAgent系统
├── MCP协议text2vector工具
│   ├── 文档解析和分块
│   ├── 向量化处理（sentence-transformers）
│   └── ChromaDB存储
├── RiskAgent智能体
│   ├── 自然语言处理
│   ├── 知识检索
│   └── 报告生成
├── 向量数据库（ChromaDB）
│   ├── 1683个文档片段
│   └── 元数据管理
└── Gradio Web界面
    ├── 智能对话
    ├── 知识检索
    ├── 文件处理
    └── 报告生成
```

## 📊 系统状态

- **知识库规模**: 1683个文档片段
- **文档来源**: Risk.md（航天质量风险管理专业文档）
- **向量模型**: all-MiniLM-L6-v2
- **数据库**: ChromaDB
- **界面框架**: Gradio 5.39.0

## 🛠️ 开发和测试

### 运行测试
```bash
# 快速系统测试
python quick_test.py

# 完整系统测试
python system_test.py

# 简单功能测试
python test_simple.py
```

### 单独测试组件
```bash
# 测试MCP工具
python mcp_text2vector.py

# 测试智能体
python risk_agent_simple.py
```

## 📁 项目文件

- `mcp_text2vector.py` - MCP协议text2vector工具
- `risk_agent_simple.py` - 简化版RiskAgent智能体
- `gradio_interface.py` - Gradio Web界面
- `deploy.py` - 部署和优化脚本
- `PROJECT_SUMMARY.md` - 项目详细总结
- `requirements.txt` - Python依赖包列表

## 🔍 使用示例

### 智能对话示例
```
用户: 什么是航天质量风险管理？
RiskAgent: 基于知识库，航天质量风险管理是指...

用户: 检索 技术风险
RiskAgent: 检索到3条相关信息：
1. 技术风险识别方法...
2. 技术风险评估标准...
3. 技术风险控制措施...
```

### 知识检索示例
- 输入关键词："风险识别"
- 系统返回相关文档片段和相似度评分
- 可以查看完整的上下文信息

## ⚠️ 注意事项

1. **首次启动**: 系统会自动加载向量模型，可能需要几分钟时间
2. **内存使用**: 建议至少2GB可用内存
3. **网络连接**: 首次运行需要下载模型文件
4. **文件格式**: 目前支持Markdown格式的文档处理

## 🆘 故障排除

### 常见问题

1. **启动失败**
   - 检查conda环境是否正确激活
   - 确认所有依赖包已安装

2. **界面无法访问**
   - 确认端口7860未被占用
   - 检查防火墙设置

3. **知识检索无结果**
   - 确认向量数据库已初始化
   - 检查Risk.md文件是否存在

### 重新初始化
```bash
# 删除现有数据库
rm -rf vector_db/

# 重新运行系统
python gradio_interface.py
```

## 📞 技术支持

如遇到问题，请检查：
1. 系统日志输出
2. 依赖包版本
3. 文件权限设置

## 📈 版本信息

- **当前版本**: v1.0
- **发布日期**: 2025-08-01
- **兼容性**: Python 3.8+, Gradio 5.0+

---

**🎉 感谢使用航天质量风险管理智能体 RiskAgent！**
