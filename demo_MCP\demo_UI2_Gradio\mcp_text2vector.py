"""
MCP协议text2vector工具
基于MCP协议构建的文本向量化工具，支持MD文件处理和向量存储
"""

import os
import json
import asyncio
from typing import List, Dict, Any, Optional
from pathlib import Path
import hashlib
import re
from datetime import datetime

import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
import markdown
from bs4 import BeautifulSoup
import pandas as pd


class MCPText2Vector:
    """MCP协议text2vector工具类"""
    
    def __init__(self, 
                 model_name: str = "all-MiniLM-L6-v2",
                 db_path: str = "./vector_db",
                 collection_name: str = "risk_documents"):
        """
        初始化text2vector工具
        
        Args:
            model_name: 句子转换模型名称
            db_path: 向量数据库路径
            collection_name: 集合名称
        """
        self.model_name = model_name
        self.db_path = db_path
        self.collection_name = collection_name
        
        # 初始化句子转换模型
        print(f"正在加载模型: {model_name}")
        self.encoder = SentenceTransformer(model_name)
        
        # 初始化ChromaDB
        self.client = chromadb.PersistentClient(
            path=db_path,
            settings=Settings(anonymized_telemetry=False)
        )
        
        # 获取或创建集合
        try:
            self.collection = self.client.get_collection(collection_name)
            print(f"已连接到现有集合: {collection_name}")
        except:
            self.collection = self.client.create_collection(
                name=collection_name,
                metadata={"description": "航天质量风险管理文档向量库"}
            )
            print(f"已创建新集合: {collection_name}")
    
    def extract_text_from_markdown(self, md_content: str) -> List[Dict[str, Any]]:
        """
        从Markdown内容中提取文本块
        
        Args:
            md_content: Markdown文本内容
            
        Returns:
            文本块列表，每个块包含内容、类型、元数据等信息
        """
        # 转换Markdown为HTML
        html = markdown.markdown(md_content, extensions=['tables', 'toc'])
        soup = BeautifulSoup(html, 'html.parser')
        
        text_blocks = []
        
        # 提取标题和段落
        for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p']):
            if element.get_text().strip():
                text_blocks.append({
                    'content': element.get_text().strip(),
                    'type': element.name,
                    'level': int(element.name[1]) if element.name.startswith('h') else 0
                })
        
        # 提取表格
        for table in soup.find_all('table'):
            # 提取表格标题（如果有）
            table_text = []
            
            # 提取表头
            headers = []
            header_row = table.find('tr')
            if header_row:
                headers = [th.get_text().strip() for th in header_row.find_all(['th', 'td'])]
                if headers:
                    table_text.append("表头: " + " | ".join(headers))
            
            # 提取表格数据
            rows = table.find_all('tr')[1:]  # 跳过表头
            for row in rows:
                cells = [td.get_text().strip() for td in row.find_all(['td', 'th'])]
                if cells and any(cell for cell in cells):  # 确保行不为空
                    table_text.append(" | ".join(cells))
            
            if table_text:
                text_blocks.append({
                    'content': "\n".join(table_text),
                    'type': 'table',
                    'level': 0,
                    'headers': headers
                })
        
        return text_blocks
    
    def chunk_text(self, text_blocks: List[Dict[str, Any]], 
                   max_chunk_size: int = 500) -> List[Dict[str, Any]]:
        """
        将文本块分割成适合向量化的片段
        
        Args:
            text_blocks: 文本块列表
            max_chunk_size: 最大片段大小（字符数）
            
        Returns:
            分割后的文本片段列表
        """
        chunks = []
        current_context = []  # 保存当前上下文（标题等）
        
        for block in text_blocks:
            content = block['content']
            block_type = block['type']
            
            # 更新上下文（保存标题信息）
            if block_type.startswith('h'):
                level = block['level']
                # 清除更低级别的标题
                current_context = [ctx for ctx in current_context if ctx['level'] < level]
                current_context.append({
                    'content': content,
                    'level': level,
                    'type': block_type
                })
                
                # 标题本身也作为一个chunk
                chunks.append({
                    'content': content,
                    'type': block_type,
                    'context': [ctx['content'] for ctx in current_context[:-1]],
                    'metadata': {
                        'block_type': block_type,
                        'level': level,
                        'is_header': True
                    }
                })
            
            elif len(content) <= max_chunk_size:
                # 内容较短，直接作为一个chunk
                chunks.append({
                    'content': content,
                    'type': block_type,
                    'context': [ctx['content'] for ctx in current_context],
                    'metadata': {
                        'block_type': block_type,
                        'is_header': False,
                        'headers': block.get('headers', [])
                    }
                })
            
            else:
                # 内容较长，需要分割
                sentences = re.split(r'[。！？；\n]', content)
                current_chunk = ""
                
                for sentence in sentences:
                    if len(current_chunk + sentence) <= max_chunk_size:
                        current_chunk += sentence + "。"
                    else:
                        if current_chunk:
                            chunks.append({
                                'content': current_chunk.strip(),
                                'type': block_type,
                                'context': [ctx['content'] for ctx in current_context],
                                'metadata': {
                                    'block_type': block_type,
                                    'is_header': False,
                                    'is_partial': True
                                }
                            })
                        current_chunk = sentence + "。"
                
                # 添加最后一个chunk
                if current_chunk:
                    chunks.append({
                        'content': current_chunk.strip(),
                        'type': block_type,
                        'context': [ctx['content'] for ctx in current_context],
                        'metadata': {
                            'block_type': block_type,
                            'is_header': False,
                            'is_partial': True
                        }
                    })
        
        return chunks
    
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        生成文本向量
        
        Args:
            texts: 文本列表
            
        Returns:
            向量列表
        """
        print(f"正在生成 {len(texts)} 个文本的向量...")
        embeddings = self.encoder.encode(texts, show_progress_bar=True)
        return embeddings.tolist()
    
    def store_vectors(self, chunks: List[Dict[str, Any]], 
                     file_path: str, file_hash: str) -> int:
        """
        存储向量到数据库
        
        Args:
            chunks: 文本片段列表
            file_path: 文件路径
            file_hash: 文件哈希值
            
        Returns:
            存储的向量数量
        """
        if not chunks:
            return 0
        
        # 准备文本和元数据
        texts = []
        metadatas = []
        ids = []
        
        for i, chunk in enumerate(chunks):
            # 构建完整文本（包含上下文）
            full_text = ""
            if chunk['context']:
                full_text = " > ".join(chunk['context']) + " > " + chunk['content']
            else:
                full_text = chunk['content']
            
            texts.append(full_text)
            
            # 构建元数据（确保所有值都是ChromaDB支持的类型）
            metadata = {
                'file_path': file_path,
                'file_hash': file_hash,
                'chunk_index': i,
                'content': chunk['content'],
                'type': chunk['type'],
                'context': json.dumps(chunk['context'], ensure_ascii=False),
                'timestamp': datetime.now().isoformat(),
                'block_type': chunk['metadata'].get('block_type', ''),
                'is_header': chunk['metadata'].get('is_header', False),
                'level': chunk['metadata'].get('level', 0),
                'is_partial': chunk['metadata'].get('is_partial', False)
            }

            # 处理headers字段（转换为字符串）
            if 'headers' in chunk['metadata'] and chunk['metadata']['headers']:
                metadata['headers'] = json.dumps(chunk['metadata']['headers'], ensure_ascii=False)
            else:
                metadata['headers'] = ""
            metadatas.append(metadata)
            
            # 生成唯一ID
            chunk_id = f"{file_hash}_{i}"
            ids.append(chunk_id)
        
        # 生成向量
        embeddings = self.generate_embeddings(texts)
        
        # 存储到数据库
        try:
            print(f"准备存储 {len(chunks)} 个文本片段...")
            print(f"示例元数据: {metadatas[0] if metadatas else 'None'}")

            self.collection.add(
                embeddings=embeddings,
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
            print(f"成功存储 {len(chunks)} 个文本片段到向量数据库")
            return len(chunks)
        except Exception as e:
            print(f"存储向量时出错: {e}")
            print(f"错误类型: {type(e)}")
            import traceback
            traceback.print_exc()
            return 0
    
    def process_markdown_file(self, file_path: str) -> Dict[str, Any]:
        """
        处理Markdown文件并存储向量
        
        Args:
            file_path: Markdown文件路径
            
        Returns:
            处理结果
        """
        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 计算文件哈希
            file_hash = hashlib.md5(content.encode('utf-8')).hexdigest()
            
            # 检查是否已经处理过
            existing = self.collection.get(
                where={"file_hash": file_hash},
                limit=1
            )
            
            if existing['ids']:
                return {
                    'success': True,
                    'message': f'文件 {file_path} 已经处理过，跳过',
                    'file_hash': file_hash,
                    'chunks_count': 0,
                    'skipped': True
                }
            
            # 提取文本块
            print(f"正在处理文件: {file_path}")
            text_blocks = self.extract_text_from_markdown(content)
            print(f"提取到 {len(text_blocks)} 个文本块")
            
            # 分割文本
            chunks = self.chunk_text(text_blocks)
            print(f"分割为 {len(chunks)} 个文本片段")
            
            # 存储向量
            stored_count = self.store_vectors(chunks, file_path, file_hash)
            
            return {
                'success': True,
                'message': f'成功处理文件 {file_path}',
                'file_hash': file_hash,
                'chunks_count': stored_count,
                'skipped': False
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'处理文件 {file_path} 时出错: {str(e)}',
                'file_hash': None,
                'chunks_count': 0,
                'skipped': False
            }
    
    def search_similar(self, query: str, n_results: int = 5) -> List[Dict[str, Any]]:
        """
        搜索相似文本
        
        Args:
            query: 查询文本
            n_results: 返回结果数量
            
        Returns:
            相似文本列表
        """
        try:
            # 生成查询向量
            query_embedding = self.encoder.encode([query]).tolist()[0]
            
            # 搜索相似向量
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                include=['documents', 'metadatas', 'distances']
            )
            
            # 格式化结果
            formatted_results = []
            for i in range(len(results['ids'][0])):
                formatted_results.append({
                    'id': results['ids'][0][i],
                    'content': results['metadatas'][0][i]['content'],
                    'context': json.loads(results['metadatas'][0][i]['context']),
                    'type': results['metadatas'][0][i]['type'],
                    'file_path': results['metadatas'][0][i]['file_path'],
                    'distance': results['distances'][0][i],
                    'similarity': 1 - results['distances'][0][i]
                })
            
            return formatted_results
            
        except Exception as e:
            print(f"搜索时出错: {e}")
            return []
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        获取集合统计信息
        
        Returns:
            统计信息
        """
        try:
            count = self.collection.count()
            
            # 获取所有文件信息
            all_data = self.collection.get(include=['metadatas'])
            files = set()
            for metadata in all_data['metadatas']:
                files.add(metadata['file_path'])
            
            return {
                'total_chunks': count,
                'total_files': len(files),
                'files': list(files),
                'collection_name': self.collection_name
            }
        except Exception as e:
            return {
                'error': str(e),
                'total_chunks': 0,
                'total_files': 0,
                'files': [],
                'collection_name': self.collection_name
            }


# MCP工具接口函数
def create_text2vector_tool():
    """创建text2vector工具实例"""
    return MCPText2Vector()


def process_file(tool: MCPText2Vector, file_path: str) -> Dict[str, Any]:
    """MCP工具接口：处理文件"""
    return tool.process_markdown_file(file_path)


def search_documents(tool: MCPText2Vector, query: str, n_results: int = 5) -> List[Dict[str, Any]]:
    """MCP工具接口：搜索文档"""
    return tool.search_similar(query, n_results)


def get_stats(tool: MCPText2Vector) -> Dict[str, Any]:
    """MCP工具接口：获取统计信息"""
    return tool.get_collection_stats()


if __name__ == "__main__":
    # 测试代码
    tool = create_text2vector_tool()
    
    # 处理Risk.md文件
    risk_file = "Risk.md"
    if os.path.exists(risk_file):
        result = process_file(tool, risk_file)
        print(f"处理结果: {result}")
        
        # 测试搜索
        search_results = search_documents(tool, "风险识别", 3)
        print(f"搜索结果: {len(search_results)} 条")
        for result in search_results:
            print(f"- {result['content'][:100]}... (相似度: {result['similarity']:.3f})")
    
    # 获取统计信息
    stats = get_stats(tool)
    print(f"统计信息: {stats}")
