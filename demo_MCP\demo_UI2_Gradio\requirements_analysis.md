# 航天质量风险管理智能体项目需求分析

## 项目概述
基于LangGraph框架搭建智能体RiskAgent，实现航天质量风险管理的智能化处理。

## 核心需求

### 1. 工具构建
- **MCP协议text2vector工具**
  - 将根目录下的"Risk.md"文件向量化并存储到vector_db数据库
  - 支持"文件导入"界面选择的MD文件向量化处理
  - 基于MCP（Model Context Protocol）协议实现

### 2. 界面设计
- **主界面风格**：类似DeepSeek主界面的简洁风格
- **标题**：显示"航天质量风险管理智能体"
- **核心组件**：问答框，不显示其他冗余信息

#### 功能模块
- **主界面**
  - 类似DeepSeek的对话框界面
  - 支持类似excel表格形式显示信息
  
- **文件导入**
  - 文件选择功能
  - 向量化处理按钮
  - 信息显示框
  - 实现MD文件向量化处理并存储到数据库

- **报告生成**
  - 检索信息并按提示词格式生成报告
  - 支持下载为MD格式

### 3. 技术规格
- **开发环境**：conda虚拟环境"langgraph-env"
- **大模型**：通过dashscope调用qwen3-max
  - Base URL: "https://dashscope.aliyuncs.com/compatible-mode/v1"
  - API Key: "st-efesdfe-test"
- **代码位置**：D:\Program\langgraph\demo_MCP\demo_UI_Gradio
- **代码要求**：简洁易懂

### 4. 数据源
- **主要文件**：Risk.md（包含航天质量风险管理相关内容）
- **文件特点**：包含大量表格数据，涵盖风险识别、分析、应对措施等

## 环境检查结果

### Conda环境状态
- ✅ langgraph-env环境已存在
- ✅ LangGraph相关包已安装：
  - langgraph: 0.6.1
  - langgraph-checkpoint: 2.1.1
  - langgraph-prebuilt: 0.6.1
  - langgraph-sdk: 0.2.0

### 已安装的关键依赖
- ✅ langchain-core: 0.3.72
- ✅ langchain-openai: 0.3.28
- ✅ langchain-mcp-adapters: 0.1.9
- ✅ openai: 1.97.1
- ✅ dashscope: 1.24.0
- ✅ faiss-cpu: 1.11.0.post1
- ✅ fastapi: 0.116.1
- ✅ uvicorn: 0.35.0

### 需要安装的包
- gradio（用于界面开发）
- sentence-transformers（用于文本向量化）
- chromadb或其他向量数据库
- markdown处理相关包

## Risk.md文件分析

### 文件结构
- 第4章：总体技术风险管理应用
- 第5章：再入飞行器技术风险管理应用  
- 第6章：箭体结构技术风险管理应用
- 第7章：动力系统技术风险管理应用

### 内容特点
- 包含大量结构化表格数据
- 涵盖风险识别、分析、应对、验证等完整流程
- 包含具体的风险案例和应用示例
- 总计5046行，内容丰富详实

### 数据价值
- 为智能体提供专业的航天风险管理知识库
- 支持基于实际案例的风险分析和建议
- 可用于生成专业的风险管理报告

## 下一步计划
1. 安装缺失的依赖包
2. 开发MCP协议text2vector工具
3. 构建LangGraph智能体框架
4. 设计向量数据库
5. 开发Gradio界面
6. 系统集成和测试
