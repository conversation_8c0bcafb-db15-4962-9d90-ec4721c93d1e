"""
航天质量风险管理智能体系统集成测试
测试所有模块的集成功能
"""

import os
import sys
import time
import traceback
from datetime import datetime

# 导入所有模块
from mcp_text2vector import MCPText2Vector, create_text2vector_tool
from risk_agent_simple import SimpleRiskAgent, get_simple_risk_agent


class SystemTester:
    """系统集成测试类"""
    
    def __init__(self):
        """初始化测试器"""
        self.test_results = []
        self.start_time = datetime.now()
        
    def log_test(self, test_name: str, success: bool, message: str = "", details: str = ""):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "details": details,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        if details:
            print(f"    详情: {details}")
    
    def test_mcp_text2vector(self):
        """测试MCP text2vector工具"""
        print("\n🔧 测试MCP text2vector工具...")
        
        try:
            # 测试工具创建
            tool = create_text2vector_tool()
            self.log_test("MCP工具创建", True, "成功创建text2vector工具实例")
            
            # 测试统计信息获取
            stats = tool.get_collection_stats()
            if "error" not in stats:
                self.log_test("统计信息获取", True, f"获取到{stats['total_chunks']}个文档片段")
            else:
                self.log_test("统计信息获取", False, f"获取统计信息失败: {stats['error']}")
            
            # 测试搜索功能
            results = tool.search_similar("风险识别", 3)
            if results and not results[0].get("error"):
                self.log_test("向量搜索", True, f"搜索到{len(results)}条结果")
            else:
                self.log_test("向量搜索", False, "搜索功能失败")
            
            return True
            
        except Exception as e:
            self.log_test("MCP工具测试", False, f"测试过程出错: {str(e)}")
            return False
    
    def test_risk_agent(self):
        """测试RiskAgent智能体"""
        print("\n🤖 测试RiskAgent智能体...")
        
        try:
            # 测试智能体创建
            agent = get_simple_risk_agent()
            self.log_test("智能体创建", True, "成功创建RiskAgent实例")
            
            # 测试基本对话
            response = agent.chat("你好")
            if response and "RiskAgent" in response:
                self.log_test("基本对话", True, "智能体正常响应")
            else:
                self.log_test("基本对话", False, f"响应异常: {response[:100] if response else 'None'}")
            
            # 测试知识检索
            response = agent.chat("检索 风险管理")
            if response and "检索到" in response:
                self.log_test("知识检索", True, "检索功能正常")
            else:
                self.log_test("知识检索", False, f"检索功能异常: {response[:100] if response else 'None'}")
            
            # 测试统计信息
            response = agent.chat("统计信息")
            if response and "知识库统计信息" in response:
                self.log_test("统计信息查询", True, "统计功能正常")
            else:
                self.log_test("统计信息查询", False, f"统计功能异常: {response[:100] if response else 'None'}")
            
            return True
            
        except Exception as e:
            self.log_test("智能体测试", False, f"测试过程出错: {str(e)}")
            return False
    
    def test_file_processing(self):
        """测试文件处理功能"""
        print("\n📁 测试文件处理功能...")
        
        try:
            agent = get_simple_risk_agent()
            
            # 测试Risk.md文件状态
            risk_file = "../../Risk.md"
            if os.path.exists(risk_file):
                self.log_test("Risk.md文件存在", True, f"文件路径: {risk_file}")
                
                # 测试文件处理（应该跳过，因为已经处理过）
                result = agent.process_markdown_file(risk_file)
                if result["success"]:
                    if result["skipped"]:
                        self.log_test("文件处理（跳过）", True, "文件已处理，正确跳过")
                    else:
                        self.log_test("文件处理（新处理）", True, f"处理了{result['chunks_count']}个片段")
                else:
                    self.log_test("文件处理", False, f"处理失败: {result['message']}")
            else:
                self.log_test("Risk.md文件存在", False, f"文件不存在: {risk_file}")
            
            return True
            
        except Exception as e:
            self.log_test("文件处理测试", False, f"测试过程出错: {str(e)}")
            return False
    
    def test_report_generation(self):
        """测试报告生成功能"""
        print("\n📊 测试报告生成功能...")
        
        try:
            agent = get_simple_risk_agent()
            
            # 测试报告生成
            report = agent.generate_risk_report(
                risk_type="技术风险",
                analysis_content="这是一个测试分析内容，用于验证报告生成功能。",
                recommendations="建议采取相应的风险控制措施。"
            )
            
            if report and "航天质量风险管理报告" in report:
                self.log_test("报告生成", True, "成功生成风险管理报告")
            else:
                self.log_test("报告生成", False, f"报告生成异常: {report[:100] if report else 'None'}")
            
            return True
            
        except Exception as e:
            self.log_test("报告生成测试", False, f"测试过程出错: {str(e)}")
            return False
    
    def test_integration(self):
        """测试系统集成功能"""
        print("\n🔗 测试系统集成功能...")
        
        try:
            agent = get_simple_risk_agent()
            
            # 测试完整工作流程：查询 -> 检索 -> 分析
            query = "什么是质量风险管理？"
            response = agent.chat(query)
            
            if response and len(response) > 50:
                self.log_test("完整工作流程", True, "查询-检索-分析流程正常")
            else:
                self.log_test("完整工作流程", False, f"流程异常: {response[:100] if response else 'None'}")
            
            # 测试多轮对话
            responses = []
            queries = ["检索 风险识别", "统计信息", "什么是技术风险？"]
            
            for i, q in enumerate(queries):
                resp = agent.chat(q)
                responses.append(resp)
                time.sleep(0.5)  # 避免过快请求
            
            if all(resp and len(resp) > 20 for resp in responses):
                self.log_test("多轮对话", True, f"成功完成{len(queries)}轮对话")
            else:
                self.log_test("多轮对话", False, "部分对话失败")
            
            return True
            
        except Exception as e:
            self.log_test("集成测试", False, f"测试过程出错: {str(e)}")
            return False
    
    def test_performance(self):
        """测试系统性能"""
        print("\n⚡ 测试系统性能...")
        
        try:
            agent = get_simple_risk_agent()
            
            # 测试响应时间
            start_time = time.time()
            response = agent.chat("检索 风险管理")
            response_time = time.time() - start_time
            
            if response_time < 10:  # 10秒内响应
                self.log_test("响应时间", True, f"响应时间: {response_time:.2f}秒")
            else:
                self.log_test("响应时间", False, f"响应过慢: {response_time:.2f}秒")
            
            # 测试内存使用（简单检查）
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            
            if memory_mb < 2000:  # 2GB内存限制
                self.log_test("内存使用", True, f"内存使用: {memory_mb:.1f}MB")
            else:
                self.log_test("内存使用", False, f"内存使用过高: {memory_mb:.1f}MB")
            
            return True
            
        except Exception as e:
            self.log_test("性能测试", False, f"测试过程出错: {str(e)}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始航天质量风险管理智能体系统集成测试")
        print("=" * 60)
        
        # 运行各项测试
        tests = [
            self.test_mcp_text2vector,
            self.test_risk_agent,
            self.test_file_processing,
            self.test_report_generation,
            self.test_integration,
            self.test_performance
        ]
        
        passed = 0
        total = 0
        
        for test_func in tests:
            try:
                if test_func():
                    passed += 1
                total += 1
            except Exception as e:
                print(f"❌ 测试函数 {test_func.__name__} 执行失败: {str(e)}")
                total += 1
        
        # 生成测试报告
        self.generate_test_report(passed, total)
    
    def generate_test_report(self, passed_tests: int, total_tests: int):
        """生成测试报告"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        print("\n" + "=" * 60)
        print("📋 测试报告")
        print("=" * 60)
        
        print(f"🕐 测试时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%H:%M:%S')}")
        print(f"⏱️ 测试耗时: {duration.total_seconds():.1f}秒")
        print(f"📊 测试结果: {passed_tests}/{total_tests} 通过")
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        print(f"📈 成功率: {success_rate:.1f}%")
        
        print("\n📝 详细结果:")
        for result in self.test_results:
            status = "✅" if result["success"] else "❌"
            print(f"{status} [{result['timestamp']}] {result['test_name']}: {result['message']}")
        
        # 总结
        print("\n🎯 测试总结:")
        if success_rate >= 90:
            print("🎉 系统运行良好，所有主要功能正常！")
        elif success_rate >= 70:
            print("⚠️ 系统基本正常，但有部分功能需要优化。")
        else:
            print("🚨 系统存在较多问题，需要进一步调试。")
        
        print("\n🔧 系统组件状态:")
        print("  • MCP text2vector工具: 已部署")
        print("  • RiskAgent智能体: 已部署")
        print("  • 向量数据库: 已初始化")
        print("  • Gradio界面: 已启动")
        print("  • Risk.md知识库: 已加载")
        
        print("\n🌐 访问地址:")
        print("  • Web界面: http://localhost:7860")
        print("  • 本地测试: python system_test.py")


def main():
    """主函数"""
    tester = SystemTester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
