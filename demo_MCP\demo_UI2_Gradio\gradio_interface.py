"""
航天质量风险管理智能体 - Gradio界面
DeepSeek风格的现代化Web界面
"""

import gradio as gr
import os
import json
from datetime import datetime
from typing import List, Dict, Any, Tuple

#from risk_agent_simple import SimpleRiskAgent, get_simple_risk_agent
from risk_agent import RiskAgent, get_risk_agent

class RiskAgentInterface:
    """RiskAgent Gradio界面类"""
    
    def __init__(self):
        """初始化界面"""
        self.agent = get_risk_agent()
        self.chat_history = []
        
    def chat_with_agent(self, message: str, history: List[List[str]]) -> Tuple[str, List[List[str]]]:
        """与智能体对话"""
        if not message.strip():
            return "", history
        
        try:
            # 调用智能体
            response = self.agent.chat(message)
            
            # 更新历史记录
            history.append([message, response])
            
            return "", history
            
        except Exception as e:
            error_response = f"处理请求时出错: {str(e)}"
            history.append([message, error_response])
            return "", history
    
    def process_uploaded_file(self, file_path: str) -> str:
        """处理上传的文件"""
        if not file_path:
            return "请选择要处理的文件"
        
        try:
            result = self.agent.process_markdown_file(file_path)
            
            if result["success"]:
                return f"""文件处理成功！
                
文件路径: {file_path}
文件哈希: {result['file_hash']}
处理片段数: {result['chunks_count']}
是否跳过: {'是' if result['skipped'] else '否'}

{result['message']}"""
            else:
                return f"文件处理失败: {result['message']}"
                
        except Exception as e:
            return f"处理文件时出错: {str(e)}"
    
    def get_knowledge_stats(self) -> str:
        """获取知识库统计信息"""
        try:
            stats = self.agent.get_vector_stats()
            
            if "error" not in stats:
                return f"""📊 知识库统计信息

🗂️ 总文档片段数: {stats['total_chunks']:,}
📁 总文件数: {stats['total_files']}
🏷️ 集合名称: {stats['collection_name']}

📋 包含文件:
{chr(10).join(f"  • {file}" for file in stats['files'])}

⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
            else:
                return f"❌ 获取统计信息失败: {stats['error']}"
                
        except Exception as e:
            return f"❌ 获取统计信息时出错: {str(e)}"
    
    def search_knowledge(self, query: str, num_results: int = 5) -> str:
        """搜索知识库"""
        if not query.strip():
            return "请输入搜索关键词"
        
        try:
            results = self.agent.search_risk_knowledge(query, num_results)
            
            if results and not results[0].get("error"):
                output = f"🔍 搜索结果 (关键词: {query})\n\n"
                
                for i, result in enumerate(results, 1):
                    output += f"📄 结果 {i}\n"
                    output += f"📝 内容: {result['content'][:300]}{'...' if len(result['content']) > 300 else ''}\n"
                    output += f"🎯 相似度: {result['similarity']:.3f}\n"
                    output += f"📂 来源: {result['file_path']}\n"
                    output += f"🏷️ 类型: {result['type']}\n"
                    if result.get('context'):
                        output += f"📍 上下文: {' > '.join(result['context'][:2])}{'...' if len(result['context']) > 2 else ''}\n"
                    output += "\n" + "─" * 50 + "\n\n"
                
                return output
            else:
                return f"❌ 搜索失败: {results[0].get('error', '未找到相关信息')}"
                
        except Exception as e:
            return f"❌ 搜索时出错: {str(e)}"
    
    def generate_report(self, risk_type: str, analysis_content: str, recommendations: str = "") -> str:
        """生成风险管理报告"""
        if not risk_type.strip() or not analysis_content.strip():
            return "请填写风险类型和分析内容"
        
        try:
            report = self.agent.generate_risk_report(risk_type, analysis_content, recommendations)
            return report
        except Exception as e:
            return f"生成报告时出错: {str(e)}"
    
    def clear_chat_history(self) -> List[List[str]]:
        """清空对话历史"""
        return []
    
    def create_interface(self) -> gr.Blocks:
        """创建Gradio界面"""
        
        # DeepSeek风格的CSS
        custom_css = """
        .gradio-container {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .header-title {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 2.5em;
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
        }
        
        .header-subtitle {
            text-align: center;
            color: #666;
            font-size: 1.1em;
            margin-bottom: 30px;
        }
        
        .tab-nav {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 10px;
            padding: 5px;
        }
        
        .chat-container {
            border: 2px solid #e1e5e9;
            border-radius: 15px;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .stats-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 10px 0;
        }
        
        .search-box {
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 15px;
        }
        
        .button-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 12px 24px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .button-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        """
        
        with gr.Blocks(css=custom_css, title="航天质量风险管理智能体") as interface:
            
            # 标题区域
            with gr.Row():
                with gr.Column():
                    gr.HTML("""
                    <div class="main-container">
                        <h1 class="header-title">🚀 航天质量风险管理智能体</h1>
                        <p class="header-subtitle">RiskAgent - 基于LangGraph和MCP协议的智能风险管理助手</p>
                    </div>
                    """)
            
            # 主要功能区域
            with gr.Tabs() as tabs:
                
                # 智能对话标签页
                with gr.TabItem("💬 智能对话", elem_classes=["tab-nav"]):
                    with gr.Row():
                        with gr.Column(scale=3):
                            chatbot = gr.Chatbot(
                                label="与RiskAgent对话",
                                height=500,
                                elem_classes=["chat-container"],
                                show_label=True,
                                avatar_images=("👤", "🤖")
                            )
                            
                            with gr.Row():
                                msg_input = gr.Textbox(
                                    label="输入消息",
                                    placeholder="请输入您的问题，例如：检索 风险识别、统计信息、什么是技术风险管理？",
                                    lines=2,
                                    scale=4
                                )
                                send_btn = gr.Button("发送", variant="primary", scale=1)
                                clear_btn = gr.Button("清空", variant="secondary", scale=1)
                        
                        with gr.Column(scale=1):
                            gr.HTML("""
                            <div class="stats-box">
                                <h3>💡 使用提示</h3>
                                <ul>
                                    <li>说"检索 [关键词]"搜索知识</li>
                                    <li>说"统计信息"查看知识库状态</li>
                                    <li>直接提问进行智能问答</li>
                                    <li>说"功能介绍"了解更多</li>
                                </ul>
                            </div>
                            """)
                            
                            stats_display = gr.Textbox(
                                label="知识库状态",
                                value=self.get_knowledge_stats(),
                                lines=10,
                                interactive=False
                            )
                            
                            refresh_stats_btn = gr.Button("刷新统计", variant="secondary")
                
                # 知识检索标签页
                with gr.TabItem("🔍 知识检索"):
                    with gr.Row():
                        with gr.Column():
                            search_input = gr.Textbox(
                                label="搜索关键词",
                                placeholder="输入要搜索的关键词，例如：风险识别、质量管理、技术风险",
                                lines=1
                            )
                            
                            with gr.Row():
                                num_results = gr.Slider(
                                    label="结果数量",
                                    minimum=1,
                                    maximum=10,
                                    value=5,
                                    step=1,
                                    scale=2
                                )
                                search_btn = gr.Button("搜索", variant="primary", scale=1)
                            
                            search_results = gr.Textbox(
                                label="搜索结果",
                                lines=20,
                                interactive=False,
                                elem_classes=["search-box"]
                            )
                
                # 文件处理标签页
                with gr.TabItem("📁 文件处理"):
                    with gr.Row():
                        with gr.Column():
                            file_upload = gr.File(
                                label="上传Markdown文件",
                                file_types=[".md", ".txt"],
                                type="filepath"
                            )
                            
                            process_btn = gr.Button("处理文件", variant="primary")
                            
                            process_result = gr.Textbox(
                                label="处理结果",
                                lines=10,
                                interactive=False
                            )
                
                # 报告生成标签页
                with gr.TabItem("📊 报告生成"):
                    with gr.Row():
                        with gr.Column():
                            risk_type_input = gr.Textbox(
                                label="风险类型",
                                placeholder="例如：技术风险、质量风险、进度风险",
                                lines=1
                            )
                            
                            analysis_input = gr.Textbox(
                                label="分析内容",
                                placeholder="请输入详细的风险分析内容...",
                                lines=8
                            )
                            
                            recommendations_input = gr.Textbox(
                                label="应对建议（可选）",
                                placeholder="请输入风险应对建议...",
                                lines=5
                            )
                            
                            generate_btn = gr.Button("生成报告", variant="primary")
                            
                            report_output = gr.Textbox(
                                label="生成的报告",
                                lines=15,
                                interactive=False
                            )
            
            # 事件绑定
            send_btn.click(
                self.chat_with_agent,
                inputs=[msg_input, chatbot],
                outputs=[msg_input, chatbot]
            )
            
            msg_input.submit(
                self.chat_with_agent,
                inputs=[msg_input, chatbot],
                outputs=[msg_input, chatbot]
            )
            
            clear_btn.click(
                self.clear_chat_history,
                outputs=[chatbot]
            )
            
            refresh_stats_btn.click(
                self.get_knowledge_stats,
                outputs=[stats_display]
            )
            
            search_btn.click(
                self.search_knowledge,
                inputs=[search_input, num_results],
                outputs=[search_results]
            )
            
            process_btn.click(
                self.process_uploaded_file,
                inputs=[file_upload],
                outputs=[process_result]
            )
            
            generate_btn.click(
                self.generate_report,
                inputs=[risk_type_input, analysis_input, recommendations_input],
                outputs=[report_output]
            )
        
        return interface


def main():
    """主函数"""
    print("🚀 启动航天质量风险管理智能体界面...")
    
    # 创建界面实例
    interface_app = RiskAgentInterface()
    
    # 创建并启动界面
    app = interface_app.create_interface()
    
    # 启动服务
    app.launch(
        server_name="127.0.0.1",
        server_port=7860,
        share=False,
        debug=True,
        show_error=True
    )


if __name__ == "__main__":
    main()
